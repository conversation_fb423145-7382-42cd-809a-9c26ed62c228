import type { Mapping } from './dataTransformer.types';

// HOD数据转换器 - 处理联合类型
const hodBidAdjustmentTransformer = (hodData: {
  adjustments: {
    adjustment: number,
    hour: number,
  }[];
  rationale: string;
} | {
  hour: number;
  adjustment: number;
  rationale: string;
}[]): {
  adjustments: {
    adjustment: number,
    hour: number,
  }[];
  rationale: string;
} => {
  if (!hodData) {
    return {
      adjustments: [],
      rationale: ''
    };
  }

  // 检查是哪种结构
  if (Array.isArray(hodData)) {
    // 第一种结构: { hour, adjustment, rationale }[] -> 转换为对象格式
    return {
      adjustments: hodData.map(item => ({
        hour: item?.hour ?? -1,
        adjustment: item?.adjustment ?? 0
      })),
      rationale: hodData[0]?.rationale ?? ''
    };
  }

  // 第二种结构: { adjustments: [], rationale: string } -> 直接返回
  if (hodData.adjustments && Array.isArray(hodData.adjustments)) {
    return {
      adjustments: hodData.adjustments,
      rationale: hodData.rationale
    };
  }

  // 未知结构，返回空对象
  return {
    adjustments: [],
    rationale: ''
  };
};

// 为 DayStrategyData 创建映射配置
export const dayStrategyConfig: { [K in keyof Strategy.DayStrategyData]: Mapping } = {
  date: {
    type: 'primitive',
    path: 'date',
    defaultValue: ''
  },
  approach: {
    type: 'primitive',
    path: 'approach',
    defaultValue: ''
  },
  rationale: {
    type: 'primitive',
    path: 'rationale',
    defaultValue: ''
  },
  // ---- 处理嵌套对象 ----
  day_budget: {
    type: 'object',
    path: 'day_budget', // API数据源
    schema: { // 子对象的映射规则
      amount: { type: 'primitive', path: 'amount', defaultValue: '' },
      yesterday: { type: 'primitive', path: 'yesterday', defaultValue: '' },
      change_from_yesterday: { type: 'primitive', path: 'change_from_yesterday', defaultValue: '' },
      adjustment_range: {
        type: 'object',
        path: 'adjustment_range',
        schema: {
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
        }
      },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  bid_adjustment_range: {
    type: 'object',
    path: 'bid_adjustment_range',
    schema: {
      typical: { type: 'primitive', path: 'typical', defaultValue: '' },
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  revision_history: {
    type: 'primitive', path: 'revision_history', defaultValue: {}
  },
  expected_results: {
    type: 'object',
    path: 'expected_results',
    schema: {
      spend: { type: 'primitive', path: 'spend', defaultValue: '' },
      sales: { type: 'primitive', path: 'sales', defaultValue: '' },
      cvr: { type: 'primitive', path: 'cvr', defaultValue: '' },
      acos: { type: 'primitive', path: 'acos', defaultValue: '' },
    }
  },
  // ---- 处理数组 ----
  campaign_budget: {
    type: 'array',
    path: 'campaign_budget', // API数据源数组
    schema: { // 数组内每个对象的映射规则
      campaign_id: { type: 'primitive', path: 'campaign_id', defaultValue: '' },
      amount: { type: 'primitive', path: 'amount', defaultValue: '' },
      benchmark_budget: { type: 'primitive', path: 'benchmark_budget', defaultValue: '' },
      change_from_weekly_avg: { type: 'primitive', path: 'change_from_weekly_avg', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
      bid_adjustment: { type: 'primitive', path: 'bid_adjustment', defaultValue: '' },
      campaign_name: { type: 'primitive', path: 'campaign_name', defaultValue: '' },
      campaign_type: { type: 'primitive', path: 'campaign_type', defaultValue: '' },
      l14d_acos: { type: 'primitive', path: 'l14d_acos', defaultValue: '' },
      swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' },
    }
  },
  // ---- 处理联合类型 ----
  hod_bid_adjustment: {
    type: 'primitive', // 我们把它当作一个需要整体转换的"原始"值
    path: 'hod_bid_adjustment',
    transformer: hodBidAdjustmentTransformer
  },
  placement_bid_adjustment: {
    type: 'array',
    path: 'placement_bid_adjustment',
    schema: {
      placement_type: { type: 'primitive', path: 'placement_type', defaultValue: '' },
      old_bid: { type: 'primitive', path: 'old_bid', defaultValue: '' },
      new_bid: { type: 'primitive', path: 'new_bid', defaultValue: '' },
      adjustment_ratio: { type: 'primitive', path: 'adjustment_ratio', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  weekly_progress_analysis: {
    type: 'object',
    path: 'weekly_progress_analysis',
    schema: {
      budget: {
        type: 'object',
        path: 'budget',
        schema: {
          week_budget: { type: 'primitive', path: 'week_budget', defaultValue: '' },
          week_budget_used: { type: 'primitive', path: 'week_budget_used', defaultValue: '' },
          week_budget_left: { type: 'primitive', path: 'week_budget_left', defaultValue: '' },
          week_budget_usage_rate: { type: 'primitive', path: 'week_budget_usage_rate', defaultValue: '' },
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          progress: { type: 'primitive', path: 'progress', defaultValue: '' },
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' },
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' },
        }
      },
      week_performance_vs_target: { type: 'primitive', path: 'week_performance_vs_target', defaultValue: '' },
      key_observations: {
        type: 'array',
        path: 'key_observations'
        // 没有 schema，表示数组元素是原始字符串值
      },
    }
  },
  ai_feedbackContent: {
    type: 'primitive',
    path: 'ai_feedbackContent',
    defaultValue: ''
  }
};
