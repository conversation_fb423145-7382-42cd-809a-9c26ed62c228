import type { Mapping } from './dataTransformer.types';

// 为 DailyAdjustmentProposal 创建映射配置
export const dailyAdjustmentProposalConfig: { [K in keyof Strategy.DailyAdjustmentProposal]: Mapping } = {
  trigger_hour: {
    type: 'primitive',
    path: 'trigger_hour',
    defaultValue: ''
  },
  overall_rationale: {
    type: 'primitive',
    path: 'overall_rationale',
    defaultValue: ''
  },
  adjustments: {
    type: 'object',
    path: 'adjustments',
    schema: {
      daily_budget: {
        type: 'object',
        path: 'daily_budget',
        schema: {
          old: { type: 'primitive', path: 'old', defaultValue: '' },
          new: { type: 'primitive', path: 'new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      daily_strategy: {
        type: 'object',
        path: 'daily_strategy',
        schema: {
          approach_old: { type: 'primitive', path: 'approach_old', defaultValue: '' },
          approach_new: { type: 'primitive', path: 'approach_new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      campaign: {
        type: 'array',
        path: 'campaign',
        schema: {
          campaign_type: { type: 'primitive', path: 'campaign_type', defaultValue: '' },
          campaign_id: { type: 'primitive', path: 'campaign_id', defaultValue: '' },
          campaign_name: { type: 'primitive', path: 'campaign_name', defaultValue: '' },
          budget_old: { type: 'primitive', path: 'budget_old', defaultValue: '' },
          budget_new: { type: 'primitive', path: 'budget_new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
          bid_old: { type: 'primitive', path: 'bid_old', defaultValue: '' },
          bid_new: { type: 'primitive', path: 'bid_new', defaultValue: '' },
          current_acos: { type: 'primitive', path: 'current_acos', defaultValue: '' },
          current_cvr: { type: 'primitive', path: 'current_cvr', defaultValue: '' },
          current_sales: { type: 'primitive', path: 'current_sales', defaultValue: '' },
          current_spend: { type: 'primitive', path: 'current_spend', defaultValue: '' },
          swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' },
          l14d_acos: { type: 'primitive', path: 'l14acos', defaultValue: '' }
        }
      },
      hod: {
        type: 'array',
        path: 'hod',
        schema: {
          hour: { type: 'primitive', path: 'hour', defaultValue: '' },
          bid_new: { type: 'primitive', path: 'bid_new', defaultValue: '' },
        },
        transformer: (hodData: any) => {
          if (hodData && Array.isArray(hodData.hod_list)) {
            return hodData.hod_list;
          }
          return hodData;
        },
      },
      hod_rationale: {
        type: 'primitive',
        path: 'hod_rationale',
        defaultValue: '',
        transformer: (rationale: any, sourceObject: any) => {
          if (rationale) {
            return rationale;
          }
          const hod = sourceObject?.hod;
          if (hod && hod.hod_list && hod.hod_list.rationale) {
            return hod.hod_list.rationale;
          }
          if (hod && hod.rationale) {
            return hod.rationale;
          }
          return '';
        },
      },
      placement: {
        type: 'array',
        path: 'placement',
        schema: {
          type: { type: 'primitive', path: 'type', defaultValue: '' },
          bid_old: { type: 'primitive', path: 'bid_old', defaultValue: '' },
          bid_new: { type: 'primitive', path: 'bid_new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
          current_acos: { type: 'primitive', path: 'current_acos', defaultValue: '' },
          current_cvr: { type: 'primitive', path: 'current_cvr', defaultValue: '' },
          current_sales: { type: 'primitive', path: 'current_sales', defaultValue: '' },
          current_spend: { type: 'primitive', path: 'current_spend', defaultValue: '' },
          swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' }
        }
      },
    }
  },
  day_progress_analysis: {
    type: 'object',
    path: 'day_progress_analysis',
    schema: {
      budget: {
        type: 'object',
        path: 'budget',
        schema: {
          day_budget: { type: 'primitive', path: 'day_budget', defaultValue: '' },
          day_budget_spend: { type: 'primitive', path: 'day_budget_spend', defaultValue: '' },
          day_budget_left: { type: 'primitive', path: 'day_budget_left', defaultValue: '' },
          current_spend_progress: { type: 'primitive', path: 'current_spend_progress', defaultValue: '' },
          target_spend_progress: { type: 'primitive', path: 'target_spend_progress', defaultValue: '' }
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          sales_target: { type: 'primitive', path: 'sales_target', defaultValue: '' },
          sales_current: { type: 'primitive', path: 'sales_current', defaultValue: '' },
          sales_left: { type: 'primitive', path: 'sales_left', defaultValue: '' },
          current_progress: { type: 'primitive', path: 'current_progress', defaultValue: '' },
          target_progress: { type: 'primitive', path: 'target_progress', defaultValue: '' }
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' }
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' }
        }
      },
      key_observations: {
        type: 'array',
        path: 'key_observations'
        // 没有 schema，表示数组元素是原始字符串值
      },
      day_performance_vs_target: { type: 'primitive', path: 'day_performance_vs_target', defaultValue: '' }
    }
  }
};
